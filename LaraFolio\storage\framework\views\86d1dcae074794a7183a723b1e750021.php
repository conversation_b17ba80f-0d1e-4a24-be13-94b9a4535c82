<div class="modal ">
    <div class="modal-content">
        <h2><?php echo e($formMode === 'edit' ? 'Edit Skill' : 'Create Skill'); ?></h2>
        <span class="close-modal">×</span>
        <hr>
        <div>
            <p>Name</p>
            <?php echo $errors->first('name', '<p class="alert">:message</p>'); ?>

            <input type="text" name="name" value="<?php echo e(isset($skill->name) ? $skill->name : ''); ?>"/>

            <p>Proficiency</p>
            <?php echo $errors->first('proficiency', '<p class="alert">:message</p>'); ?>

            <input type="text" name="proficiency" value="<?php echo e(isset($skill->proficiency) ? $skill->proficiency : ''); ?>"/>

            <p>Service</p>
            <select name="service_id" id="service_id">
                <option value="" style="display: none">Select Service</option>
                <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($service->id); ?>" <?php echo e((isset($skill->service_id) && $skill->service_id == $service->id) ? 'selected' : ''); ?>>
                        <?php echo e($service->name); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <hr>
        <div class="modal-footer">
            <button type="button" class="close-modal">
                Cancel
            </button>
            <button type="submit" class="secondary">
                <span><i class="fa fa-spinner fa-spin"></i></span>
                <?php echo e($formMode === 'edit' ? 'Update' : 'Save'); ?>

            </button>
        </div>
    </div>
</div><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/skills/form.blade.php ENDPATH**/ ?>