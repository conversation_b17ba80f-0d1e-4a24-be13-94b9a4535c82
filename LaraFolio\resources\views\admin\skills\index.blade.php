@extends('layouts.admin.base')
@section('content')
<section class="skills" id="skills">
    <div class="titlebar">
        <h1>Skills </h1>
        <button class="open-modal">New Skill</button>
    </div>
    @include('admin.skills.create')
    @include('includes.flash_message')
    <div class="table">

        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <form method="GET" action="{{ route('admin.skills.index') }}" >
            @csrf
            <div class="table-search">
                <div>
                    <select class="search-select" name="" id="">
                        <option value="">Filter Skills</option>
                    </select>
                </div>
                <div class="relative">
                    <input class="search-input" type="text" name="search" placeholder="Search Skill..." value="{{ request('search') }}">
                </div>
            </div>
        </form>

        <div class="skill_table-heading">
            <p>Name</p>
            <p>Proficiency</p>
            <p>Service</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
        @foreach ($skills as $skill)
        <div class="skill_table-items">
            <p>{{ $skill->name }}</p>
            <div class="table_skills-bar">
                <span class="table_skills-percentage" style="width: {{ $skill->proficiency }}%;"></span>
                <strong>{{ $skill->proficiency }}%</strong>
            </div>
            @if ($skill->service)
                <p>{{ $skill->service->name }}</p>
            @else
                <p></p>
            @endif
            
            <div>
                <button class="btn-icon success open-modal">
                    <i class="fas fa-pencil-alt"></i>
                </button>
                <button class="btn-icon danger" >
                    <i class="far fa-trash-alt"></i>
                </button>
            </div>
        </div>
        @include('admin.skills.edit')
        @endforeach
        <div class="table-paginate">
            {{ $skills->links('includes.pagination') }}
        </div>
    </div>
</section>

@endsection
